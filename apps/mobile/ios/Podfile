require File.join(File.dirname(`node --print "require.resolve('react-native/package.json')"`), "scripts/react_native_pods")

require 'json'
podfile_properties = JSON.parse(File.read(File.join(__dir__, 'Podfile.properties.json'))) rescue {}

# Disable new architecture for @rnmapbox/maps compatibility
ENV['RCT_NEW_ARCH_ENABLED'] = '0'

platform :ios, '16.0'
install! 'cocoapods',
  :deterministic_uuids => false

prepare_react_native_project!

# Mapbox Maps configuration for React Native 0.74 and Xcode 16 compatibility
$RNMapboxMapsDownloadToken = '*****************************************************************************************'

target 'Fishivo' do
  config = use_native_modules!

  use_frameworks! :linkage => podfile_properties['ios.useFrameworks'].to_sym if podfile_properties['ios.useFrameworks']
  use_frameworks! :linkage => ENV['USE_FRAMEWORKS'].to_sym if ENV['USE_FRAMEWORKS']

  use_react_native!(
    :path => config[:reactNativePath],
    :hermes_enabled => podfile_properties['hermes'] != 'false',
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/..",
    :privacy_file_aggregation_enabled => podfile_properties['apple.privacyManifestAggregationEnabled'] != 'false',
    # Disable codegen to fix @rnmapbox/maps compatibility issue
    :fabric_enabled => false,
    # Disable new architecture completely for Mapbox compatibility
    :new_arch_enabled => false
  )

  post_install do |installer|
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      :ccache_enabled => podfile_properties['apple.ccacheEnabled'] == 'true',
    )
    
    # Fix for Xcode 16 and MapboxMaps compatibility
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '16.0'
        
        # Fix for MapboxMaps and Xcode 16
        if target.name == 'MapboxMaps' || target.name.include?('Mapbox')
          config.build_settings['SWIFT_VERSION'] = '5.9'
          config.build_settings['ENABLE_BITCODE'] = 'NO'
          config.build_settings['SWIFT_COMPILATION_MODE'] = 'wholemodule'
          config.build_settings['SWIFT_OPTIMIZATION_LEVEL'] = '-O'
        end
      end
    end
  end
end
